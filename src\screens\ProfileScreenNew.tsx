import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StatusBar,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Haptics from 'expo-haptics';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  SlideInRight,
  ZoomIn,
} from 'react-native-reanimated';
import { useProfile } from '../contexts/ProfileContext';
import NutritionBackground from '../components/NutritionBackground';
import HealthService from '../services/HealthService';

// Removed width/height since we're using percentages for responsive design

const ProfileScreenNew: React.FC = () => {
  // Use profile context for dynamic data
  const { profile, updateProfile: updateProfileContext, dailyData, getHealthScore, calculateBMI } = useProfile();
  const [editingField, setEditingField] = useState<string | null>(null);
  const [profileImage, setProfileImage] = useState<string | null>(null);

  // Calculate BMI category from real BMI value
  const getBMICategory = (bmi: number): string => {
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  };

  // Settings states
  const [notifications, setNotifications] = useState(profile.notificationSettings.progressUpdates);
  const [darkMode, setDarkMode] = useState(false);
  const [mealReminders, setMealReminders] = useState(profile.notificationSettings.mealReminders);
  const [waterReminders, setWaterReminders] = useState(profile.notificationSettings.waterReminders);

  const updateProfile = (field: string, value: string | number | string[]) => {
    updateProfileContext(field as any, value);
    setEditingField(null);
  };



  // Load profile image on component mount
  useEffect(() => {
    loadProfileImage();
  }, []);

  const loadProfileImage = async () => {
    try {
      const savedImage = await AsyncStorage.getItem('profileImage');
      if (savedImage) {
        setProfileImage(savedImage);
      }
    } catch (error) {
      console.error('Error loading profile image:', error);
    }
  };

  const pickImage = async () => {
    try {
      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert(
          'Permission Required',
          'Please allow access to your photo library to change your profile picture.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setProfileImage(imageUri);

        // Save to AsyncStorage
        await AsyncStorage.setItem('profileImage', imageUri);

        // Haptic feedback
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const takePhoto = async () => {
    try {
      // Request camera permission
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert(
          'Permission Required',
          'Please allow camera access to take a profile picture.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setProfileImage(imageUri);

        // Save to AsyncStorage
        await AsyncStorage.setItem('profileImage', imageUri);

        // Haptic feedback
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const showImagePickerOptions = () => {
    Alert.alert(
      'Change Profile Picture',
      'Choose how you would like to update your profile picture',
      [
        { text: 'Take Photo', onPress: takePhoto },
        { text: 'Choose from Library', onPress: pickImage },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const EditableField: React.FC<{
    label: string;
    value: string | number;
    field: string;
    type?: 'text' | 'number';
    suffix?: string;
  }> = ({ label, value, field, type = 'text', suffix = '' }) => {
    const [tempValue, setTempValue] = useState(value.toString());
    const isCurrentlyEditing = editingField === field;

    const handleSave = () => {
      const finalValue = type === 'number' ? parseFloat(tempValue) || 0 : tempValue;
      updateProfile(field, finalValue);
    };

    const handleCancel = () => {
      setTempValue(value.toString());
      setEditingField(null);
    };

    return (
      <View style={styles.editableField}>
        <Text style={styles.fieldLabel}>{label}</Text>
        <View style={styles.fieldContainer}>
          {isCurrentlyEditing ? (
            <View style={styles.editingContainer}>
              <TextInput
                style={styles.fieldInput}
                value={tempValue}
                onChangeText={setTempValue}
                keyboardType={type === 'number' ? 'numeric' : 'default'}
                autoFocus
                selectTextOnFocus
              />
              <View style={styles.editActions}>
                <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                  <Ionicons name="checkmark" size={16} color="#fcf4ec" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                  <Ionicons name="close" size={16} color="#6B7C5A" />
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <TouchableOpacity 
              style={styles.fieldDisplay}
              onPress={() => setEditingField(field)}
            >
              <Text style={styles.fieldValue}>{value}{suffix}</Text>
              <Ionicons name="pencil" size={16} color="#6B7C5A" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Modern Background with NutritionBackground - positioned absolutely */}
      <View style={styles.backgroundWrapper}>
        <NutritionBackground>
          <View style={{ flex: 1 }} />
        </NutritionBackground>
      </View>

      {/* Content overlay with proper z-index */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
      >
        {/* App Logo */}
        <Animated.View entering={FadeInDown.duration(600)} style={styles.logoContainer}>
          <Image
            source={require('../../assets/image final.png')}
            style={styles.logoImage}
            resizeMode="contain"
          />
        </Animated.View>

        {/* Modern Profile Header */}
        <Animated.View entering={FadeInUp.duration(800)} style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <TouchableOpacity style={styles.avatar} onPress={showImagePickerOptions}>
              {profileImage ? (
                <Image source={{ uri: profileImage }} style={styles.avatarImage} />
              ) : (
                <Text style={styles.avatarText}>{profile.name.charAt(0)}</Text>
              )}
            </TouchableOpacity>
            <TouchableOpacity style={styles.editAvatarButton} onPress={showImagePickerOptions}>
              <Ionicons name="camera" size={16} color="#fcf4ec" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.profileInfo}>
            <EditableField
              label="Name"
              value={profile.name}
              field="name"
            />
            <EditableField
              label="Email"
              value={profile.email}
              field="email"
            />
          </View>
        </Animated.View>

        {/* Health Metrics - Editable */}
        <Animated.View entering={SlideInLeft.delay(200).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Health Metrics</Text>
          <View style={styles.metricsGrid}>
            <EditableField
              label="Age"
              value={profile.age}
              field="age"
              type="number"
              suffix=" years"
            />
            <EditableField
              label="Height"
              value={profile.height}
              field="height"
              type="number"
              suffix=" cm"
            />
            <EditableField
              label="Weight"
              value={profile.weight}
              field="weight"
              type="number"
              suffix=" kg"
            />
            <EditableField
              label="Target Weight"
              value={profile.targetWeight}
              field="targetWeight"
              type="number"
              suffix=" kg"
            />
          </View>
        </Animated.View>

        {/* Goals - Editable */}
        <Animated.View entering={SlideInRight.delay(400).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Daily Goals</Text>
          <View style={styles.goalsGrid}>
            <EditableField
              label="Calories Goal"
              value={profile.caloriesGoal}
              field="caloriesGoal"
              type="number"
              suffix=" kcal"
            />
            <EditableField
              label="Protein Goal"
              value={profile.proteinGoal}
              field="proteinGoal"
              type="number"
              suffix=" g"
            />
            <EditableField
              label="Water Goal"
              value={profile.waterGoal}
              field="waterGoal"
              type="number"
              suffix=" glasses"
            />
            <EditableField
              label="Steps Goal"
              value={profile.stepsGoal}
              field="stepsGoal"
              type="number"
              suffix=" steps"
            />
          </View>
        </Animated.View>

        {/* Activity Level */}
        <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Activity Level</Text>
          <View style={styles.activityContainer}>
            {['Sedentary', 'Light', 'Moderate', 'Active', 'Very Active'].map((level, index) => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.activityOption,
                  profile.activityLevel === level && styles.activityOptionSelected
                ]}
                onPress={() => updateProfile('activityLevel', level)}
              >
                <Text style={[
                  styles.activityText,
                  profile.activityLevel === level && styles.activityTextSelected
                ]}>
                  {level}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Dietary Preferences */}
        <Animated.View entering={SlideInLeft.delay(800).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Dietary Preferences</Text>
          <View style={styles.dietaryGrid}>
            {['Vegetarian', 'Vegan', 'Keto', 'Paleo', 'Mediterranean', 'Low Carb'].map((diet, index) => (
              <TouchableOpacity
                key={diet}
                style={[
                  styles.dietaryOption,
                  profile.dietaryPreferences?.includes(diet) && styles.dietaryOptionSelected
                ]}
                onPress={() => {
                  const current = profile.dietaryPreferences || [];
                  const updated = current.includes(diet)
                    ? current.filter((d: string) => d !== diet)
                    : [...current, diet];
                  updateProfile('dietaryPreferences', updated);
                }}
              >
                <Text style={[
                  styles.dietaryText,
                  profile.dietaryPreferences?.includes(diet) && styles.dietaryTextSelected
                ]}>
                  {diet}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Health Statistics - Real Calculated Data */}
        <Animated.View entering={FadeInUp.delay(1000).duration(600)} style={[styles.section, styles.healthStatsSection]}>
          <Text style={styles.sectionTitle}>Health Statistics</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{profile.bmi}</Text>
              <Text style={styles.statLabel}>BMI</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{getBMICategory(profile.bmi)}</Text>
              <Text style={styles.statLabel}>Category</Text>
            </View>
          </View>
        </Animated.View>

        {/* Health metrics are now handled by HealthMonitor component on homepage */}

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Changed from #fcf4ec to transparent to show background
  },
  backgroundWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0, // Behind content
  },

  scrollView: {
    flex: 1,
    paddingTop: 80, // Consistent with homepage
    zIndex: 1, // Above background
    backgroundColor: 'transparent',
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 40, // Extra bottom padding for better scrolling
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Add background for logo visibility
    marginHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  logoImage: {
    width: 120,
    height: 48,
  },
  profileHeader: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 32,
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)', // Add background to overlay properly
    marginHorizontal: 20,
    borderRadius: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 24,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 12,
    overflow: 'hidden',
  },
  avatarImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarText: {
    fontSize: 36,
    fontWeight: '700',
    color: '#fcf4ec',
    letterSpacing: -1,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#fcf4ec',
  },
  profileInfo: {
    width: '100%',
    gap: 16,
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  healthStatsSection: {
    marginBottom: 40, // Extra bottom spacing for health statistics
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 20,
    letterSpacing: -0.5,
  },
  metricsGrid: {
    gap: 16,
  },
  goalsGrid: {
    gap: 16,
  },
  editableField: {
    marginBottom: 8,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
    marginBottom: 8,
  },
  fieldContainer: {
    minHeight: 48,
  },
  fieldDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  fieldValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
  },
  editingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  fieldInput: {
    flex: 1,
    backgroundColor: '#fcf4ec',
    borderRadius: 16,
    padding: 16,
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
    borderWidth: 2,
    borderColor: '#6B7C5A',
  },
  editActions: {
    flexDirection: 'row',
    gap: 8,
  },
  saveButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activityContainer: {
    gap: 12,
  },
  activityOption: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  activityOptionSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  activityText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7C5A',
    textAlign: 'center',
  },
  activityTextSelected: {
    color: '#fcf4ec',
  },
  dietaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  dietaryOption: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  dietaryOptionSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  dietaryText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  dietaryTextSelected: {
    color: '#fcf4ec',
  },
  bottomSpacing: {
    height: 120, // Increased bottom spacing as requested
  },

  // Health Stats
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%', // Proper 2x2 grid
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    marginBottom: 16,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#6B7C5A',
    marginBottom: 4,
    textAlign: 'center',
    lineHeight: 24,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
    textAlign: 'center',
  },



  // Macro Targets
  macroContainer: {
    gap: 16,
  },
  macroItem: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  macroLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
    marginBottom: 8,
  },
  macroValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 8,
  },
  macroBar: {
    height: 8,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  macroFill: {
    height: '100%',
    borderRadius: 4,
  },
});

export default ProfileScreenNew;
