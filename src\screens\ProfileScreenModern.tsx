import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  StatusBar,
  ImageBackground,
  Alert,
  Share,
  TextInput,
  Image,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInDown,
  SlideInUp,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withDelay,
} from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernModal } from '../components/ModernModal';
import { ModernInput } from '../components/ModernInput';
import LottieIcon from '../components/LottieIcon';
import { CircularProgress } from '../components/CircularProgress';
import { TimeSelectionForm } from '../components/TimeSelectionForm';
import * as Haptics from 'expo-haptics';
import { useProfile } from '../contexts/ProfileContext';

// Removed unused dimensions

interface StatCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  progress?: number;
  index: number;
}

interface AchievementBadgeProps {
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  earned: boolean;
  progress?: number;
  index: number;
}

interface SettingItemProps {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  rightElement?: React.ReactNode;
  showChevron?: boolean;
  variant?: 'default' | 'danger' | 'premium';
}

// Modern Stat Card Component - Optimized for performance
const StatCard = React.memo(({
  title,
  value,
  subtitle,
  icon,
  color,
  progress = 0,
  index,
}: StatCardProps) => {
  // Use shared values for animations
  const scale = useSharedValue(1);
  const progressWidth = useSharedValue(0);

  // Initialize progress animation only once
  useEffect(() => {
    // Stagger animations with shorter delay for better performance
    progressWidth.value = withDelay(index * 100, withTiming(progress, { duration: 800 }));
  }, [progress, index]);

  // Memoize animated styles
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  // Optimize press handlers
  const handlePressIn = useCallback(() => {
    scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handlePressOut = useCallback(() => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
  }, []);

  // Optimize icon selection
  const iconName = useMemo(() => {
    if (icon === 'flame') return 'fire';
    if (icon === 'water') return 'water';
    if (icon === 'fitness') return 'heartbeat';
    return 'progressCircle';
  }, [icon]);

  return (
    <Animated.View
      style={[styles.statCard, animatedStyle]}
    >
      <TouchableOpacity
        style={styles.statCardButton}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <View style={styles.statCardContent}>
          <View style={styles.statCardHeader}>
            <CircularProgress
              size={50}
              progress={progress}
              color={color}
              backgroundColor={Colors.borderLight}
              strokeWidth={3}
              animationDuration={800} // Reduced animation time
            >
              <LottieIcon
                name={iconName}
                size={20}
                color={color}
                enableHaptics={false}
              />
            </CircularProgress>
            <View style={styles.statTextContainer}>
              <Text style={styles.statTitle}>{title}</Text>
              <Text style={[styles.statValue, { color }]}>{value}</Text>
              <Text style={styles.statSubtitle}>{subtitle}</Text>
            </View>
          </View>
        </View>

        {progress > 0 && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <Animated.View style={[styles.progressFill, { backgroundColor: color }, progressAnimatedStyle]} />
            </View>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
});

// Achievement Badge Component - Optimized for performance
const AchievementBadge = React.memo(({
  title,
  description,
  icon,
  earned,
  progress = 0,
}: Omit<AchievementBadgeProps, 'index'>) => {
  // Simplified animation - only use scale for earned state
  const scale = useSharedValue(earned ? 1 : 0.95);
  const opacity = useSharedValue(earned ? 1 : 0.6);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  // Memoize progress percentage to avoid recalculation
  const progressPercentage = useMemo(() => Math.round(progress), [progress]);

  return (
    <Animated.View
      style={[styles.achievementBadge, animatedStyle]}
    >
      <View style={[styles.achievementIcon, earned && styles.achievementIconEarned]}>
        <Ionicons
          name={icon}
          size={28}
          color={earned ? Colors.warning : Colors.mutedForeground}
        />
      </View>
      <View style={styles.achievementContent}>
        <Text style={[styles.achievementTitle, earned && styles.achievementTitleEarned]}>
          {title}
        </Text>
        <Text style={styles.achievementDescription}>{description}</Text>
        {!earned && progress > 0 && (
          <View style={styles.achievementProgress}>
            <View style={styles.achievementProgressBar}>
              <View style={[styles.achievementProgressFill, { width: `${progress}%` }]} />
            </View>
            <Text style={styles.achievementProgressText}>{progressPercentage}%</Text>
          </View>
        )}
      </View>
    </Animated.View>
  );
});

// Ultra-Modern Profile Header Component - Optimized for performance
const ProfileHeader = React.memo(({
  onEditProfile,
  onShowAchievements,
  onShowMealTimes,
  profileImage,
  onEditProfileImage,
  profile
}: {
  onEditProfile: () => void;
  onShowAchievements: () => void;
  onShowMealTimes: () => void;
  profileImage: string | null;
  onEditProfileImage: () => void;
  profile: any;
}) => (
    <Animated.View entering={FadeInDown.duration(800)} style={styles.modernProfileHeader}>
      <View style={styles.modernHeaderContent}>
        {/* Top Action Bar */}
        <View style={styles.modernHeaderTop}>
          <TouchableOpacity
            style={styles.modernHeaderButton}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              Alert.alert(
                'Settings',
                'What would you like to do?',
                [
                  { text: 'Edit Profile', onPress: onEditProfile },
                  { text: 'View Achievements', onPress: onShowAchievements },
                  { text: 'Set Meal Times', onPress: onShowMealTimes },
                  { text: 'Cancel', style: 'cancel' }
                ]
              );
            }}
          >
            <Ionicons name="settings-outline" size={22} color={Colors.foreground} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.modernHeaderButton}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              Share.share({ message: 'Check out NutriAI - the smartest nutrition app! 🍃' });
            }}
          >
            <Ionicons name="share-outline" size={22} color={Colors.foreground} />
          </TouchableOpacity>
        </View>

        {/* Profile Card */}
        <Animated.View entering={ZoomIn.delay(300).duration(600)} style={styles.modernProfileCard}>
          <View style={styles.modernAvatarSection}>
            <View style={styles.modernAvatarContainer}>
              {profileImage ? (
                <Image source={{ uri: profileImage }} style={styles.modernAvatarImage} />
              ) : (
                <View style={styles.modernAvatarPlaceholder}>
                  <Text style={styles.modernAvatarText}>
                    {profile.name ? profile.name.split(' ').map((n: string) => n[0]).join('') : 'U'}
                  </Text>
                </View>
              )}
              <TouchableOpacity style={styles.modernEditAvatarButton} onPress={onEditProfileImage}>
                <Ionicons name="camera" size={14} color={Colors.background} />
              </TouchableOpacity>
            </View>

            <View style={styles.modernStatusIndicator}>
              <View style={styles.modernOnlineStatus} />
            </View>
          </View>

          <View style={styles.modernUserInfo}>
            <Text style={styles.modernUserName}>{profile.name || 'User'}</Text>
            <Text style={styles.modernUserEmail}>{profile.email || 'No email set'}</Text>

            <View style={styles.modernMembershipBadge}>
              <Ionicons name="star" size={12} color={Colors.brand} />
              <Text style={styles.modernMembershipText}>Premium</Text>
            </View>
          </View>
        </Animated.View>

        {/* Action Buttons */}
        <Animated.View entering={SlideInUp.delay(500).duration(600)} style={styles.modernActionButtons}>
          <TouchableOpacity style={styles.modernActionButton} onPress={onEditProfile}>
            <Ionicons name="pencil-outline" size={18} color={Colors.brand} />
            <Text style={styles.modernActionButtonText}>Edit</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.modernActionButton} onPress={onShowAchievements}>
            <Ionicons name="trophy-outline" size={18} color={Colors.brand} />
            <Text style={styles.modernActionButtonText}>Awards</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.modernActionButton}>
            <Ionicons name="analytics-outline" size={18} color={Colors.brand} />
            <Text style={styles.modernActionButtonText}>Stats</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </Animated.View>
  ));

// Removed unused StatsCard and SettingsSection components for better performance

const ProfileScreenModern: React.FC = () => {
  // Add error boundary for profile context
  let profile, dailyData, updateProfile, refreshProfile, calculateBMI;
  try {
    const profileContext = useProfile();
    profile = profileContext.profile;
    dailyData = profileContext.dailyData;
    updateProfile = profileContext.updateProfile;
    refreshProfile = profileContext.refreshProfile;
    calculateBMI = profileContext.calculateBMI;

    console.log('📊 ProfileScreenModern: Profile context loaded successfully');
    console.log('📊 ProfileScreenModern: Profile data:', {
      name: profile?.name,
      email: profile?.email,
      weight: profile?.weight,
      height: profile?.height,
      caloriesGoal: profile?.caloriesGoal
    });
  } catch (error) {
    console.error('❌ ProfileScreenModern: Profile context error:', error);
    // Return early with error state
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fcf4ec' }}>
        <Text style={{ fontSize: 18, color: '#ff0000', textAlign: 'center', margin: 20 }}>
          Profile context error. Please restart the app.
        </Text>
      </View>
    );
  }

  // Initialize notification settings from profile - sync with profile state
  const [notifications, setNotifications] = useState(profile?.notificationSettings?.progressUpdates ?? true);
  // Removed unused darkMode state
  const [mealReminders, setMealReminders] = useState(profile?.notificationSettings?.mealReminders ?? true);
  const [waterReminders, setWaterReminders] = useState(profile?.notificationSettings?.waterReminders ?? true);

  // Sync notification states with profile changes
  useEffect(() => {
    console.log('🔄 Syncing notification states with profile:', profile.notificationSettings);
    setNotifications(profile.notificationSettings?.progressUpdates ?? true);
    setMealReminders(profile.notificationSettings?.mealReminders ?? true);
    setWaterReminders(profile.notificationSettings?.waterReminders ?? true);
  }, [profile.notificationSettings]);

  // Log profile changes for debugging
  useEffect(() => {
    console.log('📊 Profile updated:', {
      name: profile.name,
      email: profile.email,
      age: profile.age,
      height: profile.height,
      weight: profile.weight,
      targetWeight: profile.targetWeight,
      caloriesGoal: profile.caloriesGoal,
      proteinGoal: profile.proteinGoal,
      waterGoal: profile.waterGoal,
      stepsGoal: profile.stepsGoal,
      activityLevel: profile.activityLevel,
      dietaryPreferences: profile.dietaryPreferences,
      allergies: profile.allergies,
      notificationSettings: profile.notificationSettings,
      isProfileComplete: profile.isProfileComplete
    });
  }, [profile]);

  // Modal states
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [showAchievements, setShowAchievements] = useState(false);
  const [showMealTimes, setShowMealTimes] = useState(false);
  const [profileImage, setProfileImage] = useState<string | null>(null);

  // Editing states
  const [editingField, setEditingField] = useState<string | null>(null);

  // Modal editing states
  const [editingName, setEditingName] = useState('');
  const [editingEmail, setEditingEmail] = useState('');
  const [editingHeight, setEditingHeight] = useState('');
  const [editingWeight, setEditingWeight] = useState('');
  const [editingStepsGoal, setEditingStepsGoal] = useState('');

  // Loading states
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Initialize editing states when modal opens
  const initializeEditingStates = useCallback(() => {
    setEditingName(profile.name || '');
    setEditingEmail(profile.email || '');
    setEditingHeight(profile.height?.toString() || '170');
    setEditingWeight(profile.weight?.toString() || '70');
    setEditingStepsGoal(profile.stepsGoal?.toString() || '10000');
  }, [profile]);

  // Helper function to format meal times
  const formatMealTime = (time?: string) => {
    if (!time) return '8:00 AM';
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  // Handle opening edit profile modal
  const handleOpenEditProfile = useCallback(() => {
    initializeEditingStates();
    setShowEditProfile(true);
  }, [initializeEditingStates]);

  // Handle saving profile changes
  const handleSaveProfileChanges = useCallback(async () => {
    if (isSaving) return; // Prevent double saves

    setIsSaving(true);
    try {
      const updates: Partial<any> = {};

      // Validate and prepare updates
      if (editingName.trim()) updates.name = editingName.trim();
      if (editingEmail.trim()) updates.email = editingEmail.trim();

      const height = parseInt(editingHeight);
      if (!isNaN(height) && height >= 100 && height <= 250) {
        updates.height = height;
      }

      const weight = parseFloat(editingWeight);
      if (!isNaN(weight) && weight >= 30 && weight <= 200) {
        updates.weight = weight;
      }

      const stepsGoal = parseInt(editingStepsGoal);
      if (!isNaN(stepsGoal) && stepsGoal >= 1000 && stepsGoal <= 50000) {
        updates.stepsGoal = stepsGoal;
      }

      // Save all updates at once
      for (const [field, value] of Object.entries(updates)) {
        await updateProfile(field as keyof typeof profile, value);
      }

      setShowEditProfile(false);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      Alert.alert('Success', 'Profile updated successfully!');
    } catch (error) {
      console.error('❌ Error saving profile changes:', error);
      Alert.alert('Error', 'Failed to save profile changes. Please try again.');
    } finally {
      setIsSaving(false);
    }
  }, [editingName, editingEmail, editingHeight, editingWeight, editingStepsGoal, updateProfile, isSaving]);

  // Handle sign out with proper cleanup
  const handleSignOut = useCallback(async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out? This will clear all your local data.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('🔄 Starting sign out process...');

              // Clear AsyncStorage
              await AsyncStorage.multiRemove([
                'userProfile',
                'profileImage',
                'dailyData',
                'weeklyPlans',
                'mealLogs',
                'recentMeals'
              ]);

              console.log('✅ AsyncStorage cleared');

              // Reset profile to default state
              await updateProfile('name', '');
              await updateProfile('email', '');
              await updateProfile('isProfileComplete', false);

              // Clear local states
              setProfileImage(null);
              setEditingField(null);
              setShowEditProfile(false);
              setShowAchievements(false);
              setShowMealTimes(false);

              console.log('✅ Sign out completed successfully');
              Alert.alert('Success', 'You have been signed out successfully');

            } catch (error) {
              console.error('❌ Error during sign out:', error);
              Alert.alert('Error', 'Failed to sign out completely. Please try again.');
            }
          }
        }
      ]
    );
  }, []);

  // Validate profile completeness
  const validateProfileCompleteness = useCallback(() => {
    const requiredFields = ['name', 'email', 'height', 'weight', 'caloriesGoal'];
    const missingFields = requiredFields.filter(field => !profile[field as keyof typeof profile]);

    if (missingFields.length > 0) {
      console.log('⚠️ Profile incomplete. Missing fields:', missingFields);
      return false;
    }

    return true;
  }, [profile]);

  // Auto-update profile completeness status
  useEffect(() => {
    const isComplete = validateProfileCompleteness();
    if (profile.isProfileComplete !== isComplete) {
      updateProfile('isProfileComplete', isComplete);
    }
  }, [profile, validateProfileCompleteness, updateProfile]);

  // Calculate BMI category from real BMI value - Memoized for performance
  const getBMICategory = useCallback((bmi: number): string => {
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  }, []);

  // Memoize BMI calculation
  const currentBMI = useMemo(() => {
    return profile.bmi || calculateBMI();
  }, [profile.bmi, profile.height, profile.weight, calculateBMI]);

  // Update profile function with enhanced validation and error handling
  const handleUpdateProfile = useCallback(async (field: string, value: string | number | string[]) => {
    try {
      console.log(`📝 ProfileScreenModern: Starting update for field: ${field} = ${value}`);
      console.log(`📝 ProfileScreenModern: Current editing field: ${editingField}`);

      // Validate the value before updating
      if (value === null || value === undefined) {
        console.warn(`⚠️ Attempted to set ${field} to null/undefined, skipping update`);
        return;
      }

      // Validate updateProfile function exists
      if (!updateProfile) {
        console.error('❌ updateProfile function is not available');
        Alert.alert('Error', 'Profile update function is not available. Please restart the app.');
        return;
      }

      console.log(`🔄 ProfileScreenModern: Calling updateProfile for ${field}`);
      await updateProfile(field as any, value);

      console.log(`🔄 ProfileScreenModern: Setting editing field to null`);
      setEditingField(null);

      console.log(`✅ Profile field updated successfully: ${field} = ${value}`);

      // Show success feedback for important settings
      if (['caloriesGoal', 'waterGoal', 'targetWeight', 'activityLevel'].includes(field)) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    } catch (error) {
      console.error(`❌ Error updating profile field ${field}:`, error);
      Alert.alert('Error', `Failed to update ${field}. Please try again.`);
      setEditingField(null);
    }
  }, [updateProfile, editingField]);

  // Load profile image on component mount and sync with profile
  useEffect(() => {
    const loadProfileImage = async () => {
      try {
        // First check if profile has avatar URL
        if (profile.avatarUrl) {
          setProfileImage(profile.avatarUrl);
          return;
        }

        // Then check AsyncStorage for local image
        const savedImage = await AsyncStorage.getItem('profileImage');
        if (savedImage) {
          setProfileImage(savedImage);
          // Update profile with the image URL
          await updateProfile('avatarUrl', savedImage);
        }
      } catch (error) {
        console.error('❌ Error loading profile image:', error);
      }
    };

    loadProfileImage();
  }, [profile.avatarUrl, updateProfile]);

  // Refresh profile data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      console.log('🔄 Profile screen focused - refreshing profile data...');

      // Force refresh profile data from storage
      const refreshProfileData = async () => {
        try {
          await refreshProfile();
          console.log('✅ Profile data refreshed on focus');
          console.log('📊 Current profile data:', {
            name: profile.name,
            preferredMealTimes: profile.preferredMealTimes,
            caloriesGoal: profile.caloriesGoal,
            dietaryRestrictions: profile.dietaryRestrictions
          });
        } catch (error) {
          console.error('❌ Error refreshing profile on focus:', error);
        }
      };

      refreshProfileData();
    }, [refreshProfile, profile])
  );

  // Function to handle profile picture selection - Optimized with useCallback
  const selectProfileImage = useCallback(async () => {
    try {
      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera roll is required!');
        return;
      }

      // Show action sheet
      Alert.alert(
        'Select Profile Picture',
        'Choose an option',
        [
          { text: 'Camera', onPress: openCamera },
          { text: 'Photo Library', onPress: openImagePicker },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  }, []);

  const openCamera = useCallback(async () => {
    try {
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();

      if (cameraPermission.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera is required!');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setProfileImage(imageUri);

        // Save to both AsyncStorage and profile context
        try {
          await AsyncStorage.setItem('profileImage', imageUri);
          await updateProfile('avatarUrl', imageUri);
          console.log('✅ Profile image saved successfully from camera');
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        } catch (error) {
          console.error('❌ Error saving profile image from camera:', error);
          Alert.alert('Error', 'Failed to save profile image');
        }
      }
    } catch (error) {
      console.error('Error opening camera:', error);
      Alert.alert('Error', 'Failed to open camera');
    }
  }, []);

  const openImagePicker = useCallback(async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setProfileImage(imageUri);

        // Save to both AsyncStorage and profile context
        try {
          await AsyncStorage.setItem('profileImage', imageUri);
          await updateProfile('avatarUrl', imageUri);
          console.log('✅ Profile image saved successfully from gallery');
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        } catch (error) {
          console.error('❌ Error saving profile image from gallery:', error);
          Alert.alert('Error', 'Failed to save profile image');
        }
      }
    } catch (error) {
      console.error('Error opening image picker:', error);
      Alert.alert('Error', 'Failed to open image picker');
    }
  }, []);

  // Handle meal time changes - Optimized with useCallback
  const handleMealTimeChange = useCallback(async (mealType: 'breakfast' | 'lunch' | 'dinner', time: string) => {
    try {
      const updatedMealTimes = {
        ...profile.preferredMealTimes,
        [mealType]: time,
      };

      await updateProfile('preferredMealTimes', updatedMealTimes);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('Error updating meal time:', error);
    }
  }, [profile.preferredMealTimes, updateProfile]);

  // Handle notification settings changes - Enhanced with validation and error handling
  const handleNotificationChange = useCallback(async (setting: keyof typeof profile.notificationSettings, value: boolean) => {
    try {
      console.log(`🔔 Updating notification setting: ${setting} = ${value}`);

      const updatedSettings = {
        ...profile.notificationSettings,
        [setting]: value,
      };

      await updateProfile('notificationSettings', updatedSettings);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      console.log(`✅ Notification setting updated successfully: ${setting} = ${value}`);
    } catch (error) {
      console.error(`❌ Error updating notification setting ${setting}:`, error);
      Alert.alert('Error', `Failed to update ${setting} setting. Please try again.`);
    }
  }, [profile.notificationSettings, updateProfile]);

  // Dynamic stats based on real user data - Memoized for performance
  const statsData = useMemo(() => {
    if (!profile.isProfileComplete) {
      return []; // No stats for incomplete profiles
    }

    const stats = [];

    // Current streak (real data)
    if (profile.currentStreak > 0) {
      stats.push({
        title: 'Streak',
        value: profile.currentStreak.toString(),
        subtitle: 'days in a row',
        icon: 'flame' as const,
        color: Colors.warning,
        progress: Math.min((profile.currentStreak / 30) * 100, 100), // Progress towards 30-day goal
      });
    }

    // Current weight (real data)
    if (profile.weight > 0 && profile.targetWeight > 0) {
      // Calculate progress towards target weight
      const weightDifference = Math.abs(profile.weight - profile.targetWeight);
      const maxWeightDifference = Math.abs(profile.weight - (profile.targetWeight + 20)); // Assume starting 20kg away from target
      const weightProgress = Math.max(0, Math.min(100, ((maxWeightDifference - weightDifference) / maxWeightDifference) * 100));

      stats.push({
        title: 'Weight',
        value: profile.weight.toString(),
        subtitle: `target: ${profile.targetWeight}kg`,
        icon: 'fitness' as const,
        color: Colors.success,
        progress: weightProgress,
      });
    }

    // Today's calories (real data)
    if (dailyData.caloriesConsumed > 0 || profile.caloriesGoal > 0) {
      const calorieProgress = (dailyData.caloriesConsumed / profile.caloriesGoal) * 100;
      stats.push({
        title: 'Calories',
        value: dailyData.caloriesConsumed.toString(),
        subtitle: `of ${profile.caloriesGoal} goal`,
        icon: 'nutrition' as const,
        color: Colors.brand,
        progress: Math.min(calorieProgress, 100),
      });
    }

    // Water intake (real data)
    if (dailyData.waterConsumed > 0 || profile.waterGoal > 0) {
      const waterProgress = (dailyData.waterConsumed / profile.waterGoal) * 100;
      stats.push({
        title: 'Water',
        value: dailyData.waterConsumed.toString(),
        subtitle: `of ${profile.waterGoal} glasses`,
        icon: 'water' as const,
        color: Colors.info,
        progress: Math.min(waterProgress, 100),
      });
    }

    return stats;
  }, [profile.isProfileComplete, profile.currentStreak, profile.weight, profile.caloriesGoal, profile.waterGoal, dailyData.caloriesConsumed, dailyData.waterConsumed]);

  // Dynamic achievements based on real user progress - Memoized for performance
  const achievements = useMemo(() => {
    const achievementsList = [];

    // First Scan Achievement
    if (profile.totalMealsLogged > 0) {
      achievementsList.push({
        id: '1',
        title: 'First Scan',
        description: 'Scanned your first food item',
        icon: 'camera' as const,
        earned: true,
        progress: 100,
      });
    } else {
      achievementsList.push({
        id: '1',
        title: 'First Scan',
        description: 'Scan your first food item',
        icon: 'camera' as const,
        earned: false,
        progress: 0,
      });
    }

    // Week Warrior Achievement
    if (profile.currentStreak >= 7) {
      achievementsList.push({
        id: '2',
        title: 'Week Warrior',
        description: 'Tracked meals for 7 days straight',
        icon: 'calendar' as const,
        earned: true,
        progress: 100,
      });
    } else {
      achievementsList.push({
        id: '2',
        title: 'Week Warrior',
        description: 'Track meals for 7 days straight',
        icon: 'calendar' as const,
        earned: false,
        progress: Math.round((profile.currentStreak / 7) * 100),
      });
    }

    // Nutrition Expert Achievement (based on profile completeness and usage)
    const nutritionProgress = profile.isProfileComplete ? 50 : 0;
    achievementsList.push({
      id: '3',
      title: 'Nutrition Expert',
      description: 'Complete your nutrition profile',
      icon: 'school' as const,
      earned: profile.isProfileComplete,
      progress: nutritionProgress,
    });

    // Recipe Master Achievement (based on meals logged)
    const recipeProgress = Math.min((profile.totalMealsLogged / 25) * 100, 100);
    achievementsList.push({
      id: '4',
      title: 'Recipe Master',
      description: 'Log 25 meals',
      icon: 'book' as const,
      earned: profile.totalMealsLogged >= 25,
      progress: Math.round(recipeProgress),
    });

    // Goal Crusher Achievement (based on daily progress)
    const goalProgress = Math.round(profile.dailyProgress);
    achievementsList.push({
      id: '5',
      title: 'Goal Crusher',
      description: 'Achieve your daily nutrition goals',
      icon: 'trophy' as const,
      earned: profile.dailyProgress >= 100,
      progress: goalProgress,
    });

    return achievementsList;
  }, [profile.totalMealsLogged, profile.currentStreak, profile.isProfileComplete, profile.dailyProgress]);

  // Simplified Editable Field Component
  const EditableField = ({
    label,
    value,
    field,
    type = 'text',
    suffix = ''
  }: {
    label: string;
    value: string | number;
    field: string;
    type?: 'text' | 'number';
    suffix?: string;
  }) => {
    const [tempValue, setTempValue] = useState(value.toString());
    const isCurrentlyEditing = editingField === field;

    // Update temp value when value changes
    useEffect(() => {
      setTempValue(value.toString());
    }, [value]);

    const handleSave = async () => {
      try {
        const finalValue = type === 'number' ? parseFloat(tempValue) || 0 : tempValue;
        console.log(`💾 Saving ${field}: ${finalValue}`);
        await handleUpdateProfile(field, finalValue);
        console.log(`✅ ${field} saved successfully: ${finalValue}`);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } catch (error) {
        console.error(`❌ Error saving ${field}:`, error);
        Alert.alert('Error', `Failed to save ${field}. Please try again.`);
        setTempValue(value.toString());
        setEditingField(null);
      }
    };

    const handleCancel = () => {
      setTempValue(value.toString());
      setEditingField(null);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    };

    const handleEdit = () => {
      console.log(`📝 Starting edit for field: ${field}`);
      setEditingField(field);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    };

    console.log(`🔍 EditableField ${field}: isCurrentlyEditing = ${isCurrentlyEditing}, editingField = ${editingField}`);

    return (
      <View style={styles.editableField}>
        <Text style={styles.fieldLabel}>{label}</Text>
        <View style={styles.fieldContainer}>
          {isCurrentlyEditing ? (
            <View style={styles.editingContainer}>
              <TextInput
                style={styles.fieldInput}
                value={tempValue}
                onChangeText={setTempValue}
                keyboardType={type === 'number' ? 'numeric' : 'default'}
                autoFocus
                selectTextOnFocus
                placeholder={`Enter ${label.toLowerCase()}`}
              />
              <View style={styles.editActions}>
                <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                  <Ionicons name="checkmark" size={16} color="#fcf4ec" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                  <Ionicons name="close" size={16} color="#6B7C5A" />
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.fieldDisplay}
              onPress={() => {
                console.log(`👆 EditableField ${field}: Touch detected, calling handleEdit`);
                console.log(`👆 EditableField ${field}: Current editingField = ${editingField}`);
                handleEdit();
              }}
              activeOpacity={0.7}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Text style={styles.fieldValue}>{value}{suffix}</Text>
              <Ionicons name="pencil" size={16} color="#6B7C5A" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Beautiful Background with New Screens Background Image */}
      <ImageBackground
        source={require('../../assets/screens background.jpg')}
        style={styles.backgroundContainer}
        resizeMode="cover"
        onError={(error) => console.log('Background image failed to load:', error)}
        onLoad={() => console.log('Background image loaded successfully')}
      >
        <View style={styles.fcf4ecOverlay} />
      </ImageBackground>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        removeClippedSubviews={true}
        scrollEventThrottle={16}
      >
        {/* Billion-Dollar Profile Header - Now Scrollable */}
        <ProfileHeader
          onEditProfile={handleOpenEditProfile}
          onShowAchievements={() => setShowAchievements(true)}
          onShowMealTimes={() => setShowMealTimes(true)}
          profileImage={profileImage}
          onEditProfileImage={selectProfileImage}
          profile={profile}
        />

        {/* Debug Section - Remove in production */}
        {__DEV__ && (
          <View style={styles.debugSection}>
            <Text style={styles.debugTitle}>Debug Info (Dev Only)</Text>
            <Text style={styles.debugText}>Profile Complete: {profile?.isProfileComplete ? 'Yes' : 'No'}</Text>
            <Text style={styles.debugText}>Activity Level: {profile?.activityLevel || 'Not set'}</Text>
            <Text style={styles.debugText}>Dietary Prefs: {profile?.dietaryPreferences?.length || 0} items</Text>
            <Text style={styles.debugText}>Allergies: {profile?.allergies?.length || 0} items</Text>
            <Text style={styles.debugText}>Calories Goal: {profile?.caloriesGoal || 'Not set'}</Text>
            <Text style={styles.debugText}>Water Goal: {profile?.waterGoal || 'Not set'}</Text>
            <Text style={styles.debugText}>Current Editing Field: {editingField || 'None'}</Text>

            <TouchableOpacity
              style={styles.debugButton}
              onPress={() => {
                console.log('🧪 Testing edit field: weight');
                setEditingField('weight');
              }}
            >
              <Text style={styles.debugButtonText}>🧪 Test Edit Weight</Text>
            </TouchableOpacity>
          </View>
        )}
        {/* Stats Grid - Simplified for performance */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Your Progress</Text>
          {statsData.length > 0 ? (
            <View style={styles.statsGrid}>
              {statsData.map((stat, index) => (
                <StatCard
                  key={stat.title}
                  title={stat.title}
                  value={stat.value}
                  subtitle={stat.subtitle}
                  icon={stat.icon}
                  color={stat.color}
                  progress={stat.progress}
                  index={index}
                />
              ))}
            </View>
          ) : (
            <View style={styles.emptyStatsState}>
              <Ionicons name="analytics-outline" size={48} color="#E5E7EB" />
              <Text style={styles.emptyStatsText}>Complete your profile to see progress</Text>
              <Text style={styles.emptyStatsSubtext}>Set your goals and start tracking</Text>
            </View>
          )}
        </View>

        {/* Health Metrics - Editable - Simplified for performance */}
        <View style={styles.settingsContainer}>
          <Text style={styles.sectionTitle}>Health Metrics</Text>
          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <View style={styles.editableFieldsContainer}>
              <EditableField
                label="Age"
                value={profile?.age || 25}
                field="age"
                type="number"
                suffix=" years"
              />
              <EditableField
                label="Height"
                value={profile?.height || 170}
                field="height"
                type="number"
                suffix=" cm"
              />
              <EditableField
                label="Weight"
                value={profile?.weight || 70}
                field="weight"
                type="number"
                suffix=" kg"
              />
              <EditableField
                label="Target Weight"
                value={profile?.targetWeight || 65}
                field="targetWeight"
                type="number"
                suffix=" kg"
              />
            </View>
          </ModernCard>
        </View>

        {/* Daily Goals - Editable - Simplified for performance */}
        <View style={styles.settingsContainer}>
          <Text style={styles.sectionTitle}>Daily Goals</Text>
          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <View style={styles.editableFieldsContainer}>
              <EditableField
                label="Calories Goal"
                value={profile?.caloriesGoal || 2000}
                field="caloriesGoal"
                type="number"
                suffix=" kcal"
              />
              <EditableField
                label="Protein Goal"
                value={profile?.proteinGoal || 150}
                field="proteinGoal"
                type="number"
                suffix=" g"
              />
              <EditableField
                label="Water Goal"
                value={profile?.waterGoal || 8}
                field="waterGoal"
                type="number"
                suffix=" glasses"
              />
              <EditableField
                label="Steps Goal"
                value={profile?.stepsGoal || 10000}
                field="stepsGoal"
                type="number"
                suffix=" steps"
              />
            </View>
          </ModernCard>
        </View>

        {/* Health Statistics - Real Calculated Data - Simplified for performance */}
        <View style={styles.settingsContainer}>
          <Text style={styles.sectionTitle}>Health Statistics</Text>
          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <View style={styles.healthStatsGrid}>
              <View style={styles.healthStatCard}>
                <Text style={styles.healthStatValue}>{currentBMI}</Text>
                <Text style={styles.healthStatLabel}>BMI</Text>
              </View>
              <View style={styles.healthStatCard}>
                <Text style={styles.healthStatValue}>{getBMICategory(currentBMI)}</Text>
                <Text style={styles.healthStatLabel}>Category</Text>
              </View>
            </View>
          </ModernCard>
        </View>

        {/* Activity Level Selection - Simplified for performance */}
        <View style={styles.settingsContainer}>
          <Text style={styles.sectionTitle}>Activity Level</Text>
          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <View style={styles.activityContainer}>
              {['Sedentary', 'Light', 'Moderate', 'Active', 'Very Active'].map((level) => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.activityOption,
                    profile.activityLevel === level && styles.activityOptionSelected
                  ]}
                  onPress={async () => {
                    try {
                      console.log(`🏃 Setting activity level to: ${level}`);
                      await handleUpdateProfile('activityLevel', level);
                      console.log(`✅ Activity level updated successfully: ${level}`);
                    } catch (error) {
                      console.error(`❌ Error updating activity level to ${level}:`, error);
                      Alert.alert('Error', 'Failed to update activity level. Please try again.');
                    }
                  }}
                >
                  <Text style={[
                    styles.activityText,
                    profile.activityLevel === level && styles.activityTextSelected
                  ]}>
                    {level}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ModernCard>
        </View>

        {/* Dietary Preferences Selection - Simplified for performance */}
        <View style={styles.settingsContainer}>
          <Text style={styles.sectionTitle}>Dietary Preferences</Text>
          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <View style={styles.dietaryContainer}>
              {['Vegetarian', 'Vegan', 'Keto', 'Paleo', 'Mediterranean', 'Low Carb'].map((diet) => (
                <TouchableOpacity
                  key={diet}
                  style={[
                    styles.dietaryOption,
                    profile.dietaryPreferences?.includes(diet) && styles.dietaryOptionSelected
                  ]}
                  onPress={async () => {
                    try {
                      console.log(`🍽️ Toggling dietary preference: ${diet}`);
                      const current = profile.dietaryPreferences || [];
                      const updated = current.includes(diet)
                        ? current.filter((d: string) => d !== diet)
                        : [...current, diet];

                      console.log(`📝 Updating dietary preferences from [${current.join(', ')}] to [${updated.join(', ')}]`);
                      await handleUpdateProfile('dietaryPreferences', updated);

                      // Add haptic feedback
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    } catch (error) {
                      console.error(`❌ Error updating dietary preference ${diet}:`, error);
                      Alert.alert('Error', `Failed to update dietary preference. Please try again.`);
                    }
                  }}
                >
                  <Text style={[
                    styles.dietaryText,
                    profile.dietaryPreferences?.includes(diet) && styles.dietaryTextSelected
                  ]}>
                    {diet}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ModernCard>
        </View>

        {/* Settings Sections - Simplified for performance */}
        <View style={styles.settingsContainer}>
          <Text style={styles.sectionTitle}>Settings</Text>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="flag"
              title="Daily Calorie Goal"
              subtitle={`${profile.caloriesGoal || 2000} calories`}
              onPress={() => {
                Alert.prompt(
                  'Daily Calorie Goal',
                  'Enter your daily calorie goal (1200-4000 calories):',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Save',
                      onPress: async (text) => {
                        try {
                          const calories = parseInt(text || '2000');
                          console.log(`🎯 Setting calorie goal to: ${calories}`);

                          if (calories >= 1200 && calories <= 4000) {
                            await handleUpdateProfile('caloriesGoal', calories);
                            console.log(`✅ Calorie goal updated successfully: ${calories}`);
                          } else {
                            Alert.alert('Invalid Input', 'Please enter a calorie goal between 1200 and 4000.');
                          }
                        } catch (error) {
                          console.error('❌ Error updating calorie goal:', error);
                          Alert.alert('Error', 'Failed to update calorie goal. Please try again.');
                        }
                      }
                    }
                  ],
                  'plain-text',
                  (profile.caloriesGoal || 2000).toString()
                );
              }}
            />
            <SettingItem
              icon="water"
              title="Water Intake Goal"
              subtitle={`${profile.waterGoal || 8} glasses per day`}
              onPress={() => {
                Alert.prompt(
                  'Water Intake Goal',
                  'Enter your daily water goal (4-20 glasses):',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Save',
                      onPress: async (text) => {
                        try {
                          const glasses = parseInt(text || '8');
                          console.log(`💧 Setting water goal to: ${glasses} glasses`);

                          if (glasses >= 4 && glasses <= 20) {
                            await handleUpdateProfile('waterGoal', glasses);
                            console.log(`✅ Water goal updated successfully: ${glasses} glasses`);
                          } else {
                            Alert.alert('Invalid Input', 'Please enter a water goal between 4 and 20 glasses.');
                          }
                        } catch (error) {
                          console.error('❌ Error updating water goal:', error);
                          Alert.alert('Error', 'Failed to update water goal. Please try again.');
                        }
                      }
                    }
                  ],
                  'plain-text',
                  (profile.waterGoal || 8).toString()
                );
              }}
            />
            <SettingItem
              icon="fitness"
              title="Activity Level"
              subtitle={profile.activityLevel ? profile.activityLevel.charAt(0).toUpperCase() + profile.activityLevel.slice(1) : 'Not set'}
              onPress={() => {
                Alert.alert(
                  'Activity Level',
                  'Select your activity level:',
                  [
                    { text: 'Sedentary', onPress: async () => {
                      try {
                        await handleUpdateProfile('activityLevel', 'Sedentary');
                        console.log('✅ Activity level set to Sedentary');
                      } catch (error) {
                        console.error('❌ Error setting activity level:', error);
                      }
                    }},
                    { text: 'Light', onPress: async () => {
                      try {
                        await handleUpdateProfile('activityLevel', 'Light');
                        console.log('✅ Activity level set to Light');
                      } catch (error) {
                        console.error('❌ Error setting activity level:', error);
                      }
                    }},
                    { text: 'Moderate', onPress: async () => {
                      try {
                        await handleUpdateProfile('activityLevel', 'Moderate');
                        console.log('✅ Activity level set to Moderate');
                      } catch (error) {
                        console.error('❌ Error setting activity level:', error);
                      }
                    }},
                    { text: 'Active', onPress: async () => {
                      try {
                        await handleUpdateProfile('activityLevel', 'Active');
                        console.log('✅ Activity level set to Active');
                      } catch (error) {
                        console.error('❌ Error setting activity level:', error);
                      }
                    }},
                    { text: 'Very Active', onPress: async () => {
                      try {
                        await handleUpdateProfile('activityLevel', 'Very Active');
                        console.log('✅ Activity level set to Very Active');
                      } catch (error) {
                        console.error('❌ Error setting activity level:', error);
                      }
                    }},
                    { text: 'Cancel', style: 'cancel' }
                  ]
                );
              }}
            />
            <SettingItem
              icon="fitness"
              title="Weight Goal"
              subtitle={`Target: ${profile.targetWeight || 65} kg`}
              onPress={() => {
                Alert.prompt(
                  'Target Weight',
                  'Enter your target weight (30-200 kg):',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Save',
                      onPress: async (text) => {
                        try {
                          const weight = parseFloat(text || '65');
                          console.log(`🎯 Setting target weight to: ${weight} kg`);

                          if (weight >= 30 && weight <= 200) {
                            await handleUpdateProfile('targetWeight', weight);
                            console.log(`✅ Target weight updated successfully: ${weight} kg`);
                          } else {
                            Alert.alert('Invalid Input', 'Please enter a target weight between 30 and 200 kg.');
                          }
                        } catch (error) {
                          console.error('❌ Error updating target weight:', error);
                          Alert.alert('Error', 'Failed to update target weight. Please try again.');
                        }
                      }
                    }
                  ],
                  'plain-text',
                  (profile.targetWeight || 65).toString()
                );
              }}
            />
          </ModernCard>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="restaurant"
              title="Dietary Preferences"
              subtitle={profile.dietaryPreferences && profile.dietaryPreferences.length > 0 ? profile.dietaryPreferences.join(', ') : 'None set'}
              onPress={() => {
                const options = ['Vegetarian', 'Vegan', 'Keto', 'Paleo', 'Mediterranean', 'Low Carb'];
                const current = profile.dietaryPreferences || [];

                // Create a more user-friendly multi-select interface
                const showDietaryOptions = () => {
                  Alert.alert(
                    'Dietary Preferences',
                    `Current: ${current.length > 0 ? current.join(', ') : 'None'}\n\nTap to toggle preferences:`,
                    [
                      ...options.map(option => ({
                        text: `${current.includes(option) ? '✓ ' : '○ '}${option}`,
                        onPress: async () => {
                          try {
                            const updated = current.includes(option)
                              ? current.filter((d: string) => d !== option)
                              : [...current, option];

                            console.log(`🍽️ Updating dietary preferences: ${option}`);
                            await handleUpdateProfile('dietaryPreferences', updated);
                            console.log(`✅ Dietary preferences updated successfully`);

                            // Show updated selection and allow more changes
                            setTimeout(() => showDietaryOptions(), 500);
                          } catch (error) {
                            console.error('❌ Error updating dietary preferences:', error);
                            Alert.alert('Error', 'Failed to update dietary preferences. Please try again.');
                          }
                        }
                      })),
                      { text: 'Clear All', onPress: async () => {
                        try {
                          console.log('🗑️ Clearing all dietary preferences');
                          await handleUpdateProfile('dietaryPreferences', []);
                          console.log('✅ All dietary preferences cleared');
                          setTimeout(() => showDietaryOptions(), 500);
                        } catch (error) {
                          console.error('❌ Error clearing dietary preferences:', error);
                          Alert.alert('Error', 'Failed to clear dietary preferences. Please try again.');
                        }
                      }},
                      { text: 'Done', style: 'cancel' }
                    ]
                  );
                };

                showDietaryOptions();
              }}
            />
            <SettingItem
              icon="leaf"
              title="Food Allergies"
              subtitle={profile.allergies && profile.allergies.length > 0 ? profile.allergies.join(', ') : 'None'}
              onPress={() => {
                // Common allergies for quick selection
                const commonAllergies = ['Dairy', 'Eggs', 'Peanuts', 'Tree nuts', 'Shellfish', 'Wheat', 'Soy', 'Fish'];
                const current = profile.allergies || [];

                // Show options dialog first
                Alert.alert(
                  'Food Allergies',
                  'How would you like to update your allergies?',
                  [
                    {
                      text: 'Select Common Allergies',
                      onPress: () => {
                        // Create a more user-friendly multi-select interface
                        const showAllergyOptions = () => {
                          Alert.alert(
                            'Common Food Allergies',
                            `Current: ${current.length > 0 ? current.join(', ') : 'None'}\n\nTap to toggle:`,
                            [
                              ...commonAllergies.map(allergy => ({
                                text: `${current.includes(allergy) ? '✓ ' : '○ '}${allergy}`,
                                onPress: async () => {
                                  try {
                                    const updated = current.includes(allergy)
                                      ? current.filter((a: string) => a !== allergy)
                                      : [...current, allergy];

                                    console.log(`🚫 Updating allergies: ${allergy}`);
                                    await handleUpdateProfile('allergies', updated);
                                    console.log(`✅ Allergies updated successfully`);

                                    // Show updated selection and allow more changes
                                    setTimeout(() => showAllergyOptions(), 500);
                                  } catch (error) {
                                    console.error('❌ Error updating allergies:', error);
                                    Alert.alert('Error', 'Failed to update allergies. Please try again.');
                                  }
                                }
                              })),
                              { text: 'Clear All', onPress: async () => {
                                try {
                                  console.log('🗑️ Clearing all allergies');
                                  await handleUpdateProfile('allergies', []);
                                  console.log('✅ All allergies cleared');
                                  setTimeout(() => showAllergyOptions(), 500);
                                } catch (error) {
                                  console.error('❌ Error clearing allergies:', error);
                                  Alert.alert('Error', 'Failed to clear allergies. Please try again.');
                                }
                              }},
                              { text: 'Done', style: 'cancel' }
                            ]
                          );
                        };

                        showAllergyOptions();
                      }
                    },
                    {
                      text: 'Enter Custom Allergies',
                      onPress: () => {
                        Alert.prompt(
                          'Custom Food Allergies',
                          'Enter your food allergies (comma-separated):',
                          [
                            { text: 'Cancel', style: 'cancel' },
                            {
                              text: 'Save',
                              onPress: async (text) => {
                                try {
                                  const allergies = text ? text.split(',').map(a => a.trim()).filter(a => a.length > 0) : [];
                                  console.log(`🚫 Setting custom allergies: [${allergies.join(', ')}]`);
                                  await handleUpdateProfile('allergies', allergies);
                                  console.log('✅ Custom allergies updated successfully');
                                } catch (error) {
                                  console.error('❌ Error updating custom allergies:', error);
                                  Alert.alert('Error', 'Failed to update allergies. Please try again.');
                                }
                              }
                            }
                          ],
                          'plain-text',
                          profile.allergies ? profile.allergies.join(', ') : ''
                        );
                      }
                    },
                    { text: 'Cancel', style: 'cancel' }
                  ]
                );
              }}
            />
            <SettingItem
              icon="time"
              title="Meal Times"
              subtitle={profile.preferredMealTimes ?
                `Breakfast ${formatMealTime(profile.preferredMealTimes.breakfast)}, Lunch ${formatMealTime(profile.preferredMealTimes.lunch)}, Dinner ${formatMealTime(profile.preferredMealTimes.dinner)}` :
                'Not set - complete onboarding to set meal times'
              }
              onPress={() => {
                console.log('🕐 Current meal times:', profile.preferredMealTimes);
                console.log('🔍 Full profile data:', JSON.stringify(profile, null, 2));
                setShowMealTimes(true);
              }}
            />
          </ModernCard>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="notifications"
              title="Push Notifications"
              subtitle="Get reminders and updates"
              rightElement={
                <Switch
                  value={notifications}
                  onValueChange={(value) => {
                    setNotifications(value);
                    handleNotificationChange('progressUpdates', value);
                  }}
                  trackColor={{ false: Colors.muted, true: Colors.brandMuted }}
                  thumbColor={notifications ? Colors.brand : Colors.mutedForeground}
                />
              }
              showChevron={false}
            />
            <SettingItem
              icon="restaurant"
              title="Meal Reminders"
              subtitle="Remind me to log meals"
              rightElement={
                <Switch
                  value={mealReminders}
                  onValueChange={(value) => {
                    setMealReminders(value);
                    handleNotificationChange('mealReminders', value);
                  }}
                  trackColor={{ false: Colors.muted, true: Colors.brandMuted }}
                  thumbColor={mealReminders ? Colors.brand : Colors.mutedForeground}
                />
              }
              showChevron={false}
            />
            <SettingItem
              icon="water"
              title="Water Reminders"
              subtitle="Stay hydrated throughout the day"
              rightElement={
                <Switch
                  value={waterReminders}
                  onValueChange={(value) => {
                    setWaterReminders(value);
                    handleNotificationChange('waterReminders', value);
                  }}
                  trackColor={{ false: Colors.muted, true: Colors.brandMuted }}
                  thumbColor={waterReminders ? Colors.brand : Colors.mutedForeground}
                />
              }
              showChevron={false}
            />
          </ModernCard>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="help-circle"
              title="Help & Support"
              subtitle="Get help and contact support"
              onPress={() => {
                Alert.alert(
                  'Help & Support',
                  'Need help with NutriAI?\n\n• Check our FAQ section\n• Contact support team\n• Report a bug\n• Request a feature',
                  [
                    { text: 'FAQ', onPress: () => Alert.alert('FAQ', 'FAQ section coming soon!') },
                    { text: 'Contact Support', onPress: () => Alert.alert('Support', 'Email: <EMAIL>') },
                    { text: 'Close', style: 'cancel' }
                  ]
                );
              }}
            />
            <SettingItem
              icon="shield-checkmark"
              title="Privacy Policy"
              subtitle="Read our privacy policy"
              onPress={() => {
                Alert.alert(
                  'Privacy Policy',
                  'Your privacy is important to us. NutriAI:\n\n• Stores data locally on your device\n• Does not share personal information\n• Uses data only to improve your experience\n• Follows GDPR compliance standards',
                  [{ text: 'OK' }]
                );
              }}
            />
            <SettingItem
              icon="document-text"
              title="Terms of Service"
              subtitle="View terms and conditions"
              onPress={() => {
                Alert.alert(
                  'Terms of Service',
                  'By using NutriAI, you agree to:\n\n• Use the app responsibly\n• Provide accurate health information\n• Consult healthcare professionals for medical advice\n• Respect intellectual property rights',
                  [{ text: 'OK' }]
                );
              }}
            />
            <SettingItem
              icon="refresh-circle"
              title="Refresh Profile Data"
              subtitle="Force reload profile from storage"
              onPress={async () => {
                if (isRefreshing) return; // Prevent double refresh

                setIsRefreshing(true);
                console.log('🔄 Manual profile refresh triggered');
                try {
                  await refreshProfile();
                  Alert.alert('Success', 'Profile data refreshed successfully');
                  console.log('📊 Refreshed profile data:', JSON.stringify(profile, null, 2));
                } catch (error) {
                  console.error('❌ Manual refresh failed:', error);
                  Alert.alert('Error', 'Failed to refresh profile data');
                } finally {
                  setIsRefreshing(false);
                }
              }}
            />
            <SettingItem
              icon="log-out"
              title="Sign Out"
              subtitle="Sign out of your account"
              onPress={handleSignOut}
              variant="danger"
            />
          </ModernCard>
        </View>



        {/* App Version - Simplified for performance */}
        <View style={styles.versionSection}>
          <Text style={styles.versionText}>Nutri AI v1.0.0</Text>
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Edit Profile Modal */}
      <ModernModal
        visible={showEditProfile}
        onClose={() => setShowEditProfile(false)}
        title="Edit Profile"
        variant="center"
        size="lg"
      >
        <View style={styles.editProfileContent}>
          <Text style={styles.modalLabel}>Name</Text>
          <ModernInput
            value={editingName}
            onChangeText={setEditingName}
            placeholder="Enter your name"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Email</Text>
          <ModernInput
            value={editingEmail}
            onChangeText={setEditingEmail}
            placeholder="Enter your email"
            keyboardType="email-address"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Height (cm)</Text>
          <ModernInput
            value={editingHeight}
            onChangeText={setEditingHeight}
            placeholder="Enter your height (100-250 cm)"
            keyboardType="numeric"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Weight (kg)</Text>
          <ModernInput
            value={editingWeight}
            onChangeText={setEditingWeight}
            placeholder="Enter your weight (30-200 kg)"
            keyboardType="numeric"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Daily Step Goal</Text>
          <ModernInput
            value={editingStepsGoal}
            onChangeText={setEditingStepsGoal}
            placeholder="Enter your daily step goal (1000-50000)"
            keyboardType="numeric"
            variant="filled"
            style={styles.modalInput}
          />

          <View style={styles.modalButtons}>
            <ModernButton
              title="Cancel"
              onPress={() => setShowEditProfile(false)}
              variant="outline"
              size="md"
              style={styles.modalCancelButton}
            />
            <ModernButton
              title={isSaving ? "Saving..." : "Save Changes"}
              onPress={handleSaveProfileChanges}
              variant="primary"
              size="md"
              icon={isSaving ? "hourglass" : "checkmark"}
              disabled={isSaving}
              style={styles.modalSaveButton}
            />
          </View>
        </View>
      </ModernModal>

      {/* Achievements Modal */}
      <ModernModal
        visible={showAchievements}
        onClose={() => setShowAchievements(false)}
        title="Achievements"
        variant="fullscreen"
      >
        <ScrollView style={styles.achievementsScroll} showsVerticalScrollIndicator={false}>
          <View style={styles.achievementsGrid}>
            {achievements.map((achievement) => (
              <AchievementBadge
                key={achievement.title}
                title={achievement.title}
                description={achievement.description}
                icon={achievement.icon}
                earned={achievement.earned}
                progress={achievement.progress}
              />
            ))}
          </View>
        </ScrollView>
      </ModernModal>

      {/* Meal Times Modal */}
      <ModernModal
        visible={showMealTimes}
        onClose={() => setShowMealTimes(false)}
        title="Meal Times"
        variant="default"
      >
        <View style={styles.mealTimesModalContent}>
          <TimeSelectionForm
            breakfastTime={profile.preferredMealTimes?.breakfast || '08:00'}
            lunchTime={profile.preferredMealTimes?.lunch || '13:00'}
            dinnerTime={profile.preferredMealTimes?.dinner || '19:00'}
            onTimeChange={handleMealTimeChange}
          />

          <View style={styles.modalButtons}>
            <ModernButton
              title="Done"
              onPress={() => setShowMealTimes(false)}
              variant="primary"
              size="md"
              icon="checkmark"
              style={styles.modalSaveButton}
            />
          </View>
        </View>
      </ModernModal>
    </View>
  );
};

// Setting Item Component - Optimized for performance
const SettingItem = React.memo(({
  icon,
  title,
  subtitle,
  onPress,
  rightElement,
  showChevron = true,
  variant = 'default',
}: SettingItemProps) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = useCallback(() => {
    if (onPress) {
      scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
    }
  }, [onPress]);

  const handlePressOut = useCallback(() => {
    if (onPress) {
      scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    }
  }, [onPress]);

  const handlePress = useCallback(() => {
    if (onPress) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }
  }, [onPress]);

  // Dynamic styling based on variant
  const iconColor = useMemo(() => {
    switch (variant) {
      case 'danger': return Colors.destructive;
      case 'premium': return Colors.warning;
      default: return Colors.brand;
    }
  }, [variant]);

  const titleColor = useMemo(() => {
    switch (variant) {
      case 'danger': return Colors.destructive;
      default: return Colors.foreground;
    }
  }, [variant]);

  const iconBackgroundColor = useMemo(() => iconColor + '20', [iconColor]);

  return (
    <Animated.View style={[styles.settingItem, animatedStyle]}>
      <TouchableOpacity
        style={styles.settingItemButton}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={!onPress}
        activeOpacity={1}
      >
        <View style={styles.settingItemLeft}>
          <View style={[styles.settingIcon, { backgroundColor: iconBackgroundColor }]}>
            <Ionicons name={icon} size={20} color={iconColor} />
          </View>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: titleColor }]}>{title}</Text>
            {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
          </View>
        </View>

        <View style={styles.settingItemRight}>
          {rightElement}
          {showChevron && onPress && (
            <Ionicons name="chevron-forward" size={20} color={Colors.mutedForeground} />
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Make transparent to show background image
  },

  // Beautiful Background
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  fcf4ecOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(252, 244, 236, 0.85)', // 85% opacity to show background images
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 140, // Increased by 50px from 90px
  },

  // Profile Header
  profileHeader: {
    marginBottom: Spacing.xl,
  },
  headerGradient: {
    paddingTop: 60, // Reduced by 20px
    paddingBottom: Spacing.xxxl,
    paddingHorizontal: Spacing.xl,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: Spacing.xl,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: '#fcf4ec',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: Spacing.xl,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  avatarImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
  },
  statusBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 24,
    height: 24,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.success,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  userName: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
  },
  userInfo: {
    alignItems: 'center',
  },
  membershipContainer: {
    marginBottom: Spacing.lg,
  },
  membershipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    gap: Spacing.xs,
  },
  membershipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  profileActions: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  editButton: {
    flex: 1,
  },
  achievementsButton: {
    flex: 1,
  },

  // Ultra-Modern Profile Header Styles
  modernProfileHeader: {
    paddingTop: 50, // Further reduced for better balance
    paddingHorizontal: Spacing.xl,
    paddingBottom: Spacing.xl,
  },
  modernHeaderContent: {
    alignItems: 'center',
  },
  modernHeaderTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: Spacing.xl,
  },
  modernHeaderButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  modernProfileCard: {
    backgroundColor: '#fcf4ec', // Premium #fcf4ec background
    borderRadius: 24, // Modern rounded corners
    padding: 24, // Generous padding
    alignItems: 'center',
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.15)', // Subtle green border
    width: '100%',
    maxWidth: 320,
  },
  modernAvatarSection: {
    position: 'relative',
    marginBottom: Spacing.lg,
  },
  modernAvatarContainer: {
    position: 'relative',
  },
  modernAvatarImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  modernAvatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernAvatarText: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  modernEditAvatarButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  modernStatusIndicator: {
    position: 'absolute',
    top: 0,
    right: 0,
  },
  modernOnlineStatus: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.success,
    borderWidth: 2,
    borderColor: Colors.background,
  },
  modernUserInfo: {
    alignItems: 'center',
    width: '100%',
  },
  modernUserName: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  modernUserEmail: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
  },
  modernMembershipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.muted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    gap: Spacing.xs,
  },
  modernMembershipText: {
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  modernActionButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
    width: '100%',
    maxWidth: 320,
  },
  modernActionButton: {
    flex: 1,
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.xs,
  },
  modernActionButtonText: {
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },

  // Stats Section - Reduced padding to prevent text wrapping
  statsSection: {
    paddingHorizontal: Spacing.md, // Reduced from xl to md
    marginBottom: Spacing.xl,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },

  // Stat Card
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  statCardButton: {
    padding: Spacing.md, // Reduced from lg to md to prevent text wrapping
  },
  statCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  statTitle: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
    flex: 1,
  },
  statValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    marginBottom: Spacing.xs,
  },
  statSubtitle: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
  },
  progressContainer: {
    marginTop: Spacing.sm,
  },
  progressBar: {
    height: 4,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: BorderRadius.sm,
  },

  // Achievement Badge
  achievementBadge: {
    flexDirection: 'row',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  achievementIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.lg,
  },
  achievementIconEarned: {
    backgroundColor: Colors.warning + '20',
  },
  achievementContent: {
    flex: 1,
    justifyContent: 'center',
  },
  achievementTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
  },
  achievementTitleEarned: {
    color: Colors.foreground,
  },
  achievementDescription: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    lineHeight: 18,
  },
  achievementProgress: {
    marginTop: Spacing.md,
  },
  achievementProgressBar: {
    height: 4,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
    marginBottom: Spacing.xs,
  },
  achievementProgressFill: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.sm,
  },
  achievementProgressText: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    textAlign: 'right',
  },


  // Legacy Stats Card (for StatsCard component)
  statsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  statsTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: Spacing.md,
  },
  statLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },

  // Quick Actions
  quickActionsSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xl,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  quickActionButton: {
    flex: 1,
  },

  // Settings Container
  settingsContainer: {
    paddingHorizontal: Spacing.xl,
  },

  // Settings Sections
  settingsSection: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  settingsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    overflow: 'hidden',
  },
  settingItem: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  settingItemButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
    width: '100%',
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  settingSubtitle: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Sign Out Section
  signOutSection: {
    marginBottom: Spacing.xl,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.error,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.lg,
    gap: Spacing.sm,
  },
  signOutText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.error,
  },

  // Version Section
  versionSection: {
    alignItems: 'center',
    paddingBottom: Spacing.xl,
  },
  versionText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },

  // Modal Styles
  editProfileContent: {
    gap: Spacing.lg,
  },
  modalLabel: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  modalInput: {
    marginBottom: Spacing.md,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  modalCancelButton: {
    flex: 1,
  },
  modalSaveButton: {
    flex: 1,
  },
  achievementsScroll: {
    flex: 1,
  },
  achievementsGrid: {
    padding: Spacing.lg,
  },

  // Premium Stat Card Styles
  statCardContent: {
    borderRadius: 16,
    overflow: 'hidden',
    padding: 16,
  },
  statTextContainer: {
    flex: 1,
    marginLeft: 12,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },

  // Empty states
  emptyStatsState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStatsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
    marginTop: 12,
  },
  emptyStatsSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 4,
  },

  // Meal Times Modal
  mealTimesModalContent: {
    padding: 20,
  },

  // Editable Field Styles (from ProfileScreenNew)
  editableField: {
    marginBottom: 8,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
    marginBottom: 8,
  },
  fieldContainer: {
    minHeight: 48,
  },
  fieldDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    minHeight: 48,
    elevation: 1, // Add elevation for Android
    shadowColor: '#000', // Add shadow for iOS
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  fieldValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
  },
  editingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  fieldInput: {
    flex: 1,
    backgroundColor: '#fcf4ec',
    borderRadius: 16,
    padding: 16,
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
    borderWidth: 2,
    borderColor: '#6B7C5A',
  },
  editActions: {
    flexDirection: 'row',
    gap: 8,
  },
  saveButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Additional styles for new sections
  editableFieldsContainer: {
    padding: 16,
    gap: 16,
  },
  healthStatsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 16,
  },
  healthStatCard: {
    width: '48%',
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    marginBottom: 16,
  },
  healthStatValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#6B7C5A',
    marginBottom: 4,
    textAlign: 'center',
    lineHeight: 24,
  },
  healthStatLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
    textAlign: 'center',
  },

  // Activity Level Styles
  activityContainer: {
    padding: 16,
    gap: 12,
  },
  activityOption: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  activityOptionSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  activityText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7C5A',
    textAlign: 'center',
  },
  activityTextSelected: {
    color: '#fcf4ec',
  },

  // Dietary Preferences Styles
  dietaryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    padding: 16,
  },
  dietaryOption: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  dietaryOptionSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  dietaryText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  dietaryTextSelected: {
    color: '#fcf4ec',
  },

  // Debug Styles (Dev Only)
  debugSection: {
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    margin: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 0, 0, 0.3)',
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#ff0000',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  debugButton: {
    backgroundColor: '#6B7C5A',
    padding: 8,
    borderRadius: 8,
    marginTop: 8,
    alignItems: 'center',
  },
  debugButtonText: {
    color: '#fcf4ec',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default ProfileScreenModern;
